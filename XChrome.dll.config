﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="XChrome.Settings1" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <XChrome.Settings1>
            <setting name="WindowWidth" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="WindowHeight" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="WindowTop" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="WindowLeft" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="WindowState" serializeAs="String">
                <value />
            </setting>
            <setting name="WindowScheme" serializeAs="String">
                <value>0</value>
            </setting>
        </XChrome.Settings1>
    </userSettings>
</configuration>