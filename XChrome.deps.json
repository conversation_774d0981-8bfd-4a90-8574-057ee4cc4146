{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"XChrome/1.0.0": {"dependencies": {"AdonisUI.ClassicTheme": "1.17.1", "AutoHotkey.Interop": "*******", "Autoupdater.NET.Official": "1.9.2", "CommunityToolkit.Mvvm": "8.4.0", "MahApps.Metro.IconPacks": "5.1.0", "Microsoft.Playwright": "1.44.0", "MouseKeyHook": "5.7.1", "Pipelines.Sockets.Unofficial": "2.2.8", "SqlSugarCore": "*********", "System.IO.Pipelines": "10.0.0-preview.1.25080.5", "System.Management": "9.0.2", "ToastNotifications": "2.5.1", "ToastNotifications.Messages": "2.5.1", "log4net": "3.0.3"}, "runtime": {"XChrome.dll": {}}}, "AdonisUI/1.17.1": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net5.0-windows7.0/AdonisUI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AdonisUI.ClassicTheme/1.17.1": {"dependencies": {"AdonisUI": "1.17.1"}, "runtime": {"lib/net5.0-windows7.0/AdonisUI.ClassicTheme.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AutoHotkey.Interop/*******": {"runtime": {"lib/net45/AutoHotkey.Interop.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autoupdater.NET.Official/1.9.2": {"dependencies": {"Microsoft.Web.WebView2": "1.0.2592.51"}, "runtime": {"lib/net8.0-windows7.0/AutoUpdater.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"lib/net8.0-windows7.0/ar/AutoUpdater.NET.resources.dll": {"locale": "ar"}, "lib/net8.0-windows7.0/cs/AutoUpdater.NET.resources.dll": {"locale": "cs"}, "lib/net8.0-windows7.0/da/AutoUpdater.NET.resources.dll": {"locale": "da"}, "lib/net8.0-windows7.0/de/AutoUpdater.NET.resources.dll": {"locale": "de"}, "lib/net8.0-windows7.0/es/AutoUpdater.NET.resources.dll": {"locale": "es"}, "lib/net8.0-windows7.0/fr/AutoUpdater.NET.resources.dll": {"locale": "fr"}, "lib/net8.0-windows7.0/it/AutoUpdater.NET.resources.dll": {"locale": "it"}, "lib/net8.0-windows7.0/ja-JP/AutoUpdater.NET.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net8.0-windows7.0/ko/AutoUpdater.NET.resources.dll": {"locale": "ko"}, "lib/net8.0-windows7.0/lv/AutoUpdater.NET.resources.dll": {"locale": "lv"}, "lib/net8.0-windows7.0/nl/AutoUpdater.NET.resources.dll": {"locale": "nl"}, "lib/net8.0-windows7.0/pl/AutoUpdater.NET.resources.dll": {"locale": "pl"}, "lib/net8.0-windows7.0/pt-BR/AutoUpdater.NET.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows7.0/pt/AutoUpdater.NET.resources.dll": {"locale": "pt"}, "lib/net8.0-windows7.0/ru/AutoUpdater.NET.resources.dll": {"locale": "ru"}, "lib/net8.0-windows7.0/sk/AutoUpdater.NET.resources.dll": {"locale": "sk"}, "lib/net8.0-windows7.0/sv/AutoUpdater.NET.resources.dll": {"locale": "sv"}, "lib/net8.0-windows7.0/th/AutoUpdater.NET.resources.dll": {"locale": "th"}, "lib/net8.0-windows7.0/tr/AutoUpdater.NET.resources.dll": {"locale": "tr"}, "lib/net8.0-windows7.0/zh-TW/AutoUpdater.NET.resources.dll": {"locale": "zh-TW"}, "lib/net8.0-windows7.0/zh/AutoUpdater.NET.resources.dll": {"locale": "zh"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "log4net/3.0.3": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "3.0.3.0", "fileVersion": "3.0.3.0"}}}, "MahApps.Metro.IconPacks/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.BootstrapIcons": "5.1.0", "MahApps.Metro.IconPacks.BoxIcons": "5.1.0", "MahApps.Metro.IconPacks.CircumIcons": "5.1.0", "MahApps.Metro.IconPacks.Codicons": "5.1.0", "MahApps.Metro.IconPacks.Coolicons": "5.1.0", "MahApps.Metro.IconPacks.Core": "5.1.0", "MahApps.Metro.IconPacks.Entypo": "5.1.0", "MahApps.Metro.IconPacks.EvaIcons": "5.1.0", "MahApps.Metro.IconPacks.FeatherIcons": "5.1.0", "MahApps.Metro.IconPacks.FileIcons": "5.1.0", "MahApps.Metro.IconPacks.FontAwesome": "5.1.0", "MahApps.Metro.IconPacks.Fontaudio": "5.1.0", "MahApps.Metro.IconPacks.Fontisto": "5.1.0", "MahApps.Metro.IconPacks.ForkAwesome": "5.1.0", "MahApps.Metro.IconPacks.GameIcons": "5.1.0", "MahApps.Metro.IconPacks.Ionicons": "5.1.0", "MahApps.Metro.IconPacks.JamIcons": "5.1.0", "MahApps.Metro.IconPacks.Lucide": "5.1.0", "MahApps.Metro.IconPacks.Material": "5.1.0", "MahApps.Metro.IconPacks.MaterialDesign": "5.1.0", "MahApps.Metro.IconPacks.MaterialLight": "5.1.0", "MahApps.Metro.IconPacks.MemoryIcons": "5.1.0", "MahApps.Metro.IconPacks.Microns": "5.1.0", "MahApps.Metro.IconPacks.Modern": "5.1.0", "MahApps.Metro.IconPacks.Octicons": "5.1.0", "MahApps.Metro.IconPacks.PhosphorIcons": "5.1.0", "MahApps.Metro.IconPacks.PicolIcons": "5.1.0", "MahApps.Metro.IconPacks.PixelartIcons": "5.1.0", "MahApps.Metro.IconPacks.RPGAwesome": "5.1.0", "MahApps.Metro.IconPacks.RadixIcons": "5.1.0", "MahApps.Metro.IconPacks.RemixIcon": "5.1.0", "MahApps.Metro.IconPacks.SimpleIcons": "5.1.0", "MahApps.Metro.IconPacks.Typicons": "5.1.0", "MahApps.Metro.IconPacks.Unicons": "5.1.0", "MahApps.Metro.IconPacks.VaadinIcons": "5.1.0", "MahApps.Metro.IconPacks.WeatherIcons": "5.1.0", "MahApps.Metro.IconPacks.Zondicons": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.BootstrapIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.BoxIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.CircumIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Codicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Codicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Coolicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Core/5.1.0": {"dependencies": {"System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Entypo/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Entypo.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.EvaIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.FeatherIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.FileIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Fontaudio/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.FontAwesome/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Fontisto/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.ForkAwesome/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.GameIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Ionicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.JamIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Lucide/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Lucide.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Material/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Material.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.MaterialDesign/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.MaterialLight/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.MemoryIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Microns/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Microns.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Modern/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Modern.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Octicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Octicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.PhosphorIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.PicolIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.PixelartIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.RadixIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.RemixIcon/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.RPGAwesome/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.SimpleIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Typicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Typicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Unicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Unicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.VaadinIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.WeatherIcons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "MahApps.Metro.IconPacks.Zondicons/5.1.0": {"dependencies": {"MahApps.Metro.IconPacks.Core": "5.1.0"}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.Data.Sqlite/9.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Data.Sqlite.Core/9.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52902"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Playwright/1.44.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Playwright.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Web.WebView2/1.0.2592.51": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2592.51", "fileVersion": "1.0.2592.51"}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.2592.51"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.2592.51"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.2592.51"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {}, "MouseKeyHook/5.7.1": {"runtime": {"lib/net7.0-windows7.0/Gma.System.MouseKeyHook.dll": {"assemblyVersion": "5.7.1.0", "fileVersion": "5.7.1.0"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net7.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.5.0"}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.2.27524"}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}}, "Oracle.ManagedDataAccess.Core/3.21.100": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.1"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "10.0.0-preview.1.25080.5"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "9.0.0", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.2", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "3.21.100", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.6.0", "SqlSugarCore.Kdbndp": "9.3.7.207", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "5.1.4.177", "fileVersion": "5.1.4.177"}}}, "SqlSugarCore.Dm/8.6.0": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.27409", "fileVersion": "8.3.1.27409"}}}, "SqlSugarCore.Kdbndp/9.3.7.207": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.7.207", "fileVersion": "9.3.7.207"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/9.0.2": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}}, "System.DirectoryServices.Protocols/6.0.1": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/10.0.0-preview.1.25080.5": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}}}, "System.Management/9.0.2": {"dependencies": {"System.CodeDom": "9.0.2"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.ProtectedData/8.0.0": {}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/8.0.5": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}}, "ToastNotifications/2.5.1": {"runtime": {"lib/net40/ToastNotifications.dll": {"assemblyVersion": "2.5.1.0", "fileVersion": "2.5.1.0"}}}, "ToastNotifications.Messages/2.5.1": {"dependencies": {"ToastNotifications": "2.5.1"}, "runtime": {"lib/net40/ToastNotifications.Messages.dll": {"assemblyVersion": "2.5.1.0", "fileVersion": "2.5.1.0"}}}}}, "libraries": {"XChrome/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdonisUI/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-A23SWpxbj6aGaKbGjyDBDJYV1xtkN03uu50iXcyF+m4mxr0vw/IbOLKPSTmclzPEE4hxnHCC4Ysl827HzfLhaw==", "path": "adonisui/1.17.1", "hashPath": "adonisui.1.17.1.nupkg.sha512"}, "AdonisUI.ClassicTheme/1.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-SMTro4yZ22iNczHII3osyrcuBnH69sRmQsUhV5lg7NPdPZWkYaULK0OtyVriDynmNGqqwRwJ+t4Sc0hBK7jT0g==", "path": "adonisui.classictheme/1.17.1", "hashPath": "adonisui.classictheme.1.17.1.nupkg.sha512"}, "AutoHotkey.Interop/*******": {"type": "package", "serviceable": true, "sha512": "sha512-ZeJLYFmNDthkw/xj0kKDiQYaBWNHb5q54CqGd71vZ/jggi0xGKoMUA5pVQmT5k/+ssy3WFDw5NcBlAqSr2gy2g==", "path": "autohotkey.interop/*******", "hashPath": "autohotkey.interop.*******.nupkg.sha512"}, "Autoupdater.NET.Official/1.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-wVB0YMk5Dyc2UMUb46t5TVvzipfgkNHtuKh04wp14k/32nY7wMTz8gSMgiaX9WAHesW60K6J8uGz5G8gyGkEPQ==", "path": "autoupdater.net.official/1.9.2", "hashPath": "autoupdater.net.official.1.9.2.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "log4net/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-LAZBO0rHo4DM56GIt124NaXKo6LVZcfiuWVgODdUnhPNMzTOlRJWOEIXo8zVNLm426B+coDc4ocfTsTw7R7Zeg==", "path": "log4net/3.0.3", "hashPath": "log4net.3.0.3.nupkg.sha512"}, "MahApps.Metro.IconPacks/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+R8cYdrG+xvHeYoiNh/K2mZLBP9+x6b8odEei+eGy2/DJvtwZw1G9fXjwO43X5pEkmyJLLeNchKIBadY7qn2zA==", "path": "mahapps.metro.iconpacks/5.1.0", "hashPath": "mahapps.metro.iconpacks.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.BootstrapIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-wdJqJbbko/lwFQvQhhnq2+cWW3KT5RyQ01brukJ+Xl4KxTsNv7ClGTLqFesG1gp+O81mIK6trjCnQgt3d1TnGQ==", "path": "mahapps.metro.iconpacks.bootstrapicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.bootstrapicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.BoxIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-FcpJ8PZ6g6Vk/4ORknsb62AL22Gh+woWFUOp6FAs/msyTgCwz0xMEuLj+wVnvy4AK1JRzqAWfJciYC8zgtjtMw==", "path": "mahapps.metro.iconpacks.boxicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.boxicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.CircumIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YF<PERSON>rBWVcCWZsLdWvFGN8m4XngakVhKpjRo62Ps7WaRnA3D8x0gMLpY+3lmgr42O3gIIUWjJ7u+UKKaAty4tUOg==", "path": "mahapps.metro.iconpacks.circumicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.circumicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Codicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z8aIgtHRW9kxqxv+W/Pg1dyRTlLhmVjGCOnpCfa2hmRg/pNxBCcVSTa6zKOnB1zitW8u4HA50mchUoFzGuzC6g==", "path": "mahapps.metro.iconpacks.codicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.codicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Coolicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TqaeiTMhWZKXHB42sKc2bpBOKDK6m0dJzYtyBPdCsn+kYbxFSvpGlNOxWQ0iuDyfbo+Ouksxs6PH0G2QDPz+Ow==", "path": "mahapps.metro.iconpacks.coolicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.coolicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Core/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DBvN+gsmx5iryZO4VCcrYH1iaV1o5Qwed+IZ4bElvfRqUcNs3+sqA6Y2lPYdzLDDtuEZ681fkWyEzlxe5f1pcQ==", "path": "mahapps.metro.iconpacks.core/5.1.0", "hashPath": "mahapps.metro.iconpacks.core.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Entypo/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ePuwcIy6VDuD3Kc5Opu4dfCsdnt4Z2zImhas3UXnwj/tCpq6GFT8cI1MraWOhUaf5lhokHFsS2033qrPuhIupQ==", "path": "mahapps.metro.iconpacks.entypo/5.1.0", "hashPath": "mahapps.metro.iconpacks.entypo.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.EvaIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-6JFvb9TTSbaKjHKFOWzroSqm+9RlagJJbOMNIo4va2wYWOdZT62Gjx+txW4gbRMlODV9lolQeUgikkm3I/M9ng==", "path": "mahapps.metro.iconpacks.evaicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.evaicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.FeatherIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-fuBDFMn2YUMVex12yDXdh+H1Fp1XzqaF66omMweiS7SkELPQeE7e9CXI9kXe6XQZvgwqRz2um4KbpHH1JZiyUw==", "path": "mahapps.metro.iconpacks.feathericons/5.1.0", "hashPath": "mahapps.metro.iconpacks.feathericons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.FileIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrfKti0Z626E9fu9zpiPK3hCchwR8hcWy9vn75wTR4lQAq++22a1k4b3b3BGHXDSYqtP9+WolnQR6fiTrLPMkQ==", "path": "mahapps.metro.iconpacks.fileicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.fileicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Fontaudio/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-k9ttxX1LYz7IKXANBbdPWz9x/1Qn8jwki+N67+Vw9S8hanIrKZuoT63fqioRvmw+aHAmSuk2g1+uffydS9Ef6g==", "path": "mahapps.metro.iconpacks.fontaudio/5.1.0", "hashPath": "mahapps.metro.iconpacks.fontaudio.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.FontAwesome/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZu5L29vYg97rXxvqOwDJAlmyq7gn0Vt07GdRdC0+DEY5uLBmutWOQPHexveE73X/m+1G4C5j4yZZ6SmjSWtvg==", "path": "mahapps.metro.iconpacks.fontawesome/5.1.0", "hashPath": "mahapps.metro.iconpacks.fontawesome.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Fontisto/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-q2uwuZwCk6UOoVr3ks90PH2vWRchXCGzBSXJUvCxyQ2IUVNP0a1AL/fsYeB+sHtEDgsC7k/EXOgi4xJkk968Dg==", "path": "mahapps.metro.iconpacks.fontisto/5.1.0", "hashPath": "mahapps.metro.iconpacks.fontisto.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.ForkAwesome/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-h9zsdYzXyA6ZpjqTg0ADnhTlxyCKzreBKgDS7cmO0aB1gBAt8dRYvuJGhYvcRlFbuY4GAXqBkfOVceQ5HbaFXw==", "path": "mahapps.metro.iconpacks.forkawesome/5.1.0", "hashPath": "mahapps.metro.iconpacks.forkawesome.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.GameIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-poBiGz9rPGOjgxNu+XhEYQZXJFQ0AAzN3lbgxyKxmg2Q8OdRVeIhw1I6aFBHOPcEiVpGLXRu+6lLZU83iAZnCA==", "path": "mahapps.metro.iconpacks.gameicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.gameicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Ionicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TXM9az0HAdcF9UirpGbrZFCRs0UMFj7DBTnx/2EtiAwUV1rVpMmC0XhdbmlnDfCMuAGOUOYFueVb6Iah6FsHng==", "path": "mahapps.metro.iconpacks.ionicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.ionicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.JamIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-QPVveu9Me7ny+ewPmRv6/gWXCM43FO+iAcvd1OqNZLdwqVQem7gGACRBMxGYBqGk+hbbk0WzYkluAwiN/sNePA==", "path": "mahapps.metro.iconpacks.jamicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.jamicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Lucide/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-7nZgyQnpo3mkHagcVkI2VpcyPEau2QzP5ysem49hrFOIG8zp1AGOvitxoJAP2THK+MT8vYM9vreVuxJzSVDPNg==", "path": "mahapps.metro.iconpacks.lucide/5.1.0", "hashPath": "mahapps.metro.iconpacks.lucide.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Material/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RWh43z0Ddd3jS0ktXpU6Y2DbvaNV8gY8uBVfmOuy7LrRzcLhd0ywUkWQrH1F3m6ODIO1vgMI5vEcKgqXP58s9Q==", "path": "mahapps.metro.iconpacks.material/5.1.0", "hashPath": "mahapps.metro.iconpacks.material.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.MaterialDesign/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YPe3yCobg+HHTsDIKbOcWj/k+nZL/JZTekZmQGxdK2SLXRb66VF8bkB1/459Tu+1kvo7qS/QPwk8jofUIkhRPA==", "path": "mahapps.metro.iconpacks.materialdesign/5.1.0", "hashPath": "mahapps.metro.iconpacks.materialdesign.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.MaterialLight/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-887pN4nAIqVg+oS3O1MQY3lF4Xqj9vixNN4y4ji0wIWlZjUZoM0DeMnLo6eRSZSa1PC2k+SmOpXQWy3ahB/+Xg==", "path": "mahapps.metro.iconpacks.materiallight/5.1.0", "hashPath": "mahapps.metro.iconpacks.materiallight.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.MemoryIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vAEA+3xxMmKVHAd4SSuECVxOgG8BIrRaxMo43POAWka8P+H1oZZNheZtRnGa0nJvtcOzX630dke9DR0xQuo8HQ==", "path": "mahapps.metro.iconpacks.memoryicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.memoryicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Microns/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UP8JY/5Vs2R/fFQacRGV0aAFbu1MUs1FHHC2zslVWiXuXvZN2X70vUWNXtmA8Ww58lpWAgpjXEh76nUxC0f2LQ==", "path": "mahapps.metro.iconpacks.microns/5.1.0", "hashPath": "mahapps.metro.iconpacks.microns.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Modern/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-DdTbEAEv12OH8wi6Vle8jy1APeShdrCR5UPutL3QKkSplyZdy2Kr6rsr+6rocM+2eDe2vEMEllvljQXcV4NaEA==", "path": "mahapps.metro.iconpacks.modern/5.1.0", "hashPath": "mahapps.metro.iconpacks.modern.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Octicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWMwHnfQ70dbfmxasgm+smPZN8z2kkRY0UBpi+S+PJMTPdG0y3TKq2hOvoot8Ob0gqj2ecY+wUGjglBFHYrwVw==", "path": "mahapps.metro.iconpacks.octicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.octicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.PhosphorIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-d1h663ZWkacOOBZyL/6kOTh+mX58zhsLdEqTSLnQQLtkz3FJ3cugdj2wZIm3mPpj3UMuJFgfnt5aMnvO+AZhIg==", "path": "mahapps.metro.iconpacks.phosphoricons/5.1.0", "hashPath": "mahapps.metro.iconpacks.phosphoricons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.PicolIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1gpm7uLF2CIxlV5I2T/oq7Fc+94WeB53ulCwjEN+Itz1oZtlcd98hqVHCByWwpf9kQCf6iDpmExO7LvwLIaNNQ==", "path": "mahapps.metro.iconpacks.picolicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.picolicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.PixelartIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-5ciUi0PsIvfuOjxWQjTrbYPC1Hzdyxhpqwm9cP/+8D8JmxT5o7GdOQ361murKtXeY20DH36l0sXZFTixPKoEtQ==", "path": "mahapps.metro.iconpacks.pixelarticons/5.1.0", "hashPath": "mahapps.metro.iconpacks.pixelarticons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.RadixIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6f+8tZfmprzKyYXM6xqf+BbayMIwofN7ix+FJxtreU7Z4/X6y8YprFxhX5GU2zmSDtf0QxFLEtQsEPOikYhGw==", "path": "mahapps.metro.iconpacks.radixicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.radixicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.RemixIcon/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MY5KqzmCFnq7D8cm0BR9dqLbEUz3ns2u7IpMAi/+G5daWMUM3CrXhNErx2vuEgeQi6AbkdHvtozEkV9BsiwQEA==", "path": "mahapps.metro.iconpacks.remixicon/5.1.0", "hashPath": "mahapps.metro.iconpacks.remixicon.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.RPGAwesome/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-dX4t9A3V6GDzUFZ3h4I66k5KDnPNepGUwCC+8biMiWTmqGMTe++gcXFUhAjlUkm65VANIk4qdtwiXZjkMTMWmA==", "path": "mahapps.metro.iconpacks.rpgawesome/5.1.0", "hashPath": "mahapps.metro.iconpacks.rpgawesome.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.SimpleIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-m3tdU4BzVHSpI8AfPXYHDm9VgLClgaHdhyGKvOlLFPmBPF8bT2ib26oldIFgZTensxDVy5Xq3FO/WI0ANR6n8Q==", "path": "mahapps.metro.iconpacks.simpleicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.simpleicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Typicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-A3yLUxyJgJ5RDcFkA3VPI43l8vMDeIVUcSwfqbTHvuskjwdGF4whOENEIb6Y8085ZszETZsyDogvvk5khBvw6g==", "path": "mahapps.metro.iconpacks.typicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.typicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Unicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZpt9quy7spHNcoNcuLXYBEaggUvDVQzrjw8ZaQnNDyqvT2IY/4EHAdYcrPXMhPSJGMYCxRB6uwSxsRKR9jMyA==", "path": "mahapps.metro.iconpacks.unicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.unicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.VaadinIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4uzL65rIPuJtO1F1/5Pv4yMtOaonBROYho8VyJ+ifWbQzF7n+ElmEHQin0ntuIOmpB8bmb+2THptsRA+ICZxjA==", "path": "mahapps.metro.iconpacks.vaadinicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.vaadinicons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.WeatherIcons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-McjFxvUcYo03Imut0/nxTrIfJyV7FR4V1ShDke427aNMTSLQ1+MYbVoX8XNPd2i6iXL5rO6qDnyIRYKsGxystA==", "path": "mahapps.metro.iconpacks.weathericons/5.1.0", "hashPath": "mahapps.metro.iconpacks.weathericons.5.1.0.nupkg.sha512"}, "MahApps.Metro.IconPacks.Zondicons/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EiSwb2763RiczWsbeJJ+8TuZOW4wXGNAF0a8MAULNyCd7CREgjzKNB+vxXqEMXbVYhM5VcAtZSddQB/EhEuBsQ==", "path": "mahapps.metro.iconpacks.zondicons/5.1.0", "hashPath": "mahapps.metro.iconpacks.zondicons.5.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lw6wthgXGx3r/U775k1UkUAWIn0kAT0wj4ZRq0WlhPx4WAOiBsIjgDKgWkXcNTGT0KfHiClkM+tyPVFDvxeObw==", "path": "microsoft.data.sqlite/9.0.0", "hashPath": "microsoft.data.sqlite.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cFfZjFL+tqzGYw9lB31EkV1IWF5xRQNk2k+MQd+Cf86Gl6zTeAoiZIFw5sRB1Z8OxpEC7nu+nTDsLSjieBAPTw==", "path": "microsoft.data.sqlite.core/9.0.0", "hashPath": "microsoft.data.sqlite.core.9.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Playwright/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-stag3Jv/FtjqR5/8UcVfyYyktPt9AHxBnNazyytu4cq5W8/J5DQDQEednk/cK2iGy2Y/dC0hUcCdxp9cFDckmA==", "path": "microsoft.playwright/1.44.0", "hashPath": "microsoft.playwright.1.44.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2592.51": {"type": "package", "serviceable": true, "sha512": "sha512-AC9aWCthS2JvddYA1jl4dFpLBW3GsLRInhp5dkcBzaFXsRehfoUN9olIUsrH41eNaNYd7z9NRvmy81aUA5aD1g==", "path": "microsoft.web.webview2/1.0.2592.51", "hashPath": "microsoft.web.webview2.1.0.2592.51.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MouseKeyHook/5.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0VhB6cbcOpMOcoCQmvPMVqxaH76S4IPHwpbCIY7XKUNimdj9+81rX+Evnu5O53xYgUvdD7g0qmlVeZP2wGCnA==", "path": "mousekeyhook/5.7.1", "hashPath": "mousekeyhook.5.7.1.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.100": {"type": "package", "serviceable": true, "sha512": "sha512-nsqyUE+v246WB0SOnR1u9lfZxYoNcdj1fRjTt7TOhCN0JurEc6+qu+mMe+dl1sySB2UpyWdfqHG1iSQJYaXEfA==", "path": "oracle.manageddataaccess.core/3.21.100", "hashPath": "oracle.manageddataaccess.core.3.21.100.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-LI1+X4Za7aE0GGZ8XYJE0oDA8sR1rMT2HEEfm/syzzeht61QTIz2lR6V6r5ZqQaxVP1eJ5mO8zNBePdPADCtUw==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/8.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q0NAjF9hvkxLbNedIrCqKd3uru0enzZ49GaQtenvsLDQ29aHwlSqg1mRkVYxZ/85UYJFgXh+XHqABSrMgun4aw==", "path": "sqlsugarcore.dm/8.6.0", "hashPath": "sqlsugarcore.dm.8.6.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.7.207": {"type": "package", "serviceable": true, "sha512": "sha512-MNwycbMAUTigrHjUcSvGiv0hSN3jiLUrM9MUj1k4S9rrDtxpkVsvfbTOX2es3DGtYHqn7kS5GeC29g0s9GuoDQ==", "path": "sqlsugarcore.kdbndp/9.3.7.207", "hashPath": "sqlsugarcore.kdbndp.9.3.7.207.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-VnlzS7afE8LaXXwKdHOe2D6FcDekqThzU1E67CONV7gp71q3zposqcSeXH+PxARMXC5j31efwXrxP8VGvD70Ug==", "path": "system.codedom/9.0.2", "hashPath": "system.codedom.9.0.2.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ndUZlEkAMc1XzM0xGN++SsJrNhRkIHaKI8+te325vrUgoLT1ufWNI6KB8FFrL7NpRMHPrdxP99aF3fHbAPxW0A==", "path": "system.directoryservices.protocols/6.0.1", "hashPath": "system.directoryservices.protocols.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/10.0.0-preview.1.25080.5": {"type": "package", "serviceable": true, "sha512": "sha512-nks5yfev/2sY3ARiirjmKxsJEwRJU1DbRoaDzaUY1s3qlwbtMYxtvURHL412Cc4/yS/LLXkJDDFhlf/8ez9hiQ==", "path": "system.io.pipelines/10.0.0-preview.1.25080.5", "hashPath": "system.io.pipelines.10.0.0-preview.1.25080.5.nupkg.sha512"}, "System.Management/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-U6SXFe1kfJySAvcPPdbdb+fycaY+3c/KV0PwTjurvrALMnlSm37s5z8zcoI7qbkV2kYhxiLfsZRGiF0XeSsqSQ==", "path": "system.management/9.0.2", "hashPath": "system.management.9.0.2.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "path": "system.runtime.caching/8.0.0", "hashPath": "system.runtime.caching.8.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "ToastNotifications/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-0ZBWG0FCPafwbIzNhjWgC0GEOyk51GF3rYWrh5QJ61ZxDEoLgdDsEUt8HMxYN7lRLVt/mWhtgdbe4OmhXfwtkA==", "path": "toastnotifications/2.5.1", "hashPath": "toastnotifications.2.5.1.nupkg.sha512"}, "ToastNotifications.Messages/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-KG4oMi61ShmRuPK3/yAFhUph/rZGYmS8IcsIvF2Fk3np11fQo6ZjdK2XUmFnY9b0oQY9IilxhCmOdVGoOGEeHQ==", "path": "toastnotifications.messages/2.5.1", "hashPath": "toastnotifications.messages.2.5.1.nupkg.sha512"}}}