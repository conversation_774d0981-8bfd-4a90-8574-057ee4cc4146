﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<!-- Info 日志文件 Appender：只记录 INFO 级别的日志 -->
	<appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
		<!-- 日志文件前缀，后面会附加日期后缀 -->
		<file value="logs/info" />
		<appendToFile value="true" />
		<!-- rollingStyle 设置为 Date，表示按日期切割 -->
		<rollingStyle value="Date" />
		<!-- 格式为 .yyyyMMdd.log，生成示例文件名 info.20250207.log -->
		<datePattern value="'.'yyyyMMdd'.log'" />
		<!-- 设置为 false 表示文件名将包含日期 -->
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %-5level %logger - %message%newline" />
		</layout>
		<!-- 过滤器：仅接受 INFO 级别 -->
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
			<acceptOnMatch value="true" />
		</filter>
		<!-- 拒绝其它所有级别 -->
		<filter type="log4net.Filter.DenyAllFilter" />
	</appender>

	<!-- Error 日志文件 Appender：记录 ERROR 和 FATAL 级别日志 -->
	<appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
		<file value="logs/error" />
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<datePattern value="'.'yyyyMMdd'.log'" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %-5level %logger - %message%newline" />
		</layout>
		<!-- 过滤器：接收 ERROR 至 FATAL 级别 -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="ERROR" />
			<levelMax value="FATAL" />
			<acceptOnMatch value="true" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
	</appender>

	<!-- Root Logger 引用上面的两个 Appender -->
	<root>
		<!-- 设置最低输出级别（此处设为 DEBUG，可根据需要调整） -->
		<level value="DEBUG" />
		<appender-ref ref="InfoFileAppender" />
		<appender-ref ref="ErrorFileAppender" />
	</root>
</log4net>